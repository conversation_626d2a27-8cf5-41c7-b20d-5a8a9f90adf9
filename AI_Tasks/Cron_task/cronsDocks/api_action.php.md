# api_action.php - Core API Action Dispatcher

**File Path:** `api/cli/api_action.php`  
**Type:** Core cron script  
**Status:** Active - Critical system component  

## Purpose and Logic Description

The `api_action.php` script serves as the primary command-line dispatcher for various API-related cron tasks. It acts as a centralized entry point for multiple data loading, processing, and synchronization operations within the SDBP (Smart Digital Bus Platform) system.

## Schedule/Frequency

This script is executed with different actions at various intervals:

### High-Frequency Operations (Every 1 minute)
- `bill_emv_transaction` - EMV transaction billing
- `check_emv_abonements` - EMV abonement verification

### Medium-Frequency Operations
- `ride_all` (date_end_shift=0) - Every 5 minutes
- `load_all_terminal_data` - Every 8 minutes
- `waypoints` - Every 15 minutes
- `load_emv_stop_list` - Every 20 minutes (chained with stoplist recalculation)
- `reload_city` - Every 21 minutes
- `ride_all` (date_end_shift=2) - Every 30 minutes

## Actions Supported

### 1. load_all_terminal_data
**Purpose:** Loads terminal data from external sources  
**Parameters:**
- `--partner_id=1` - Partner identifier
- `--http_host=api.sdbp.54dev.tkp2.prod` - API host
- `--forks=1` - Number of parallel processes
**Output:** `/tkp2/sdbp54devprod/log/cron-load-all-terminal-data.log`

### 2. bill_emv_transaction
**Purpose:** Processes EMV transaction billing  
**Parameters:**
- `--max_count=5000` - Maximum transactions per run
- `--partner_id=1` - Partner identifier
- `--forks=1` - Number of parallel processes
- `--ordered=1` - Process in order
**Output:** `/tkp2/sdbp54devprod/log/bill-emv-transaction.log`

### 3. check_emv_abonements
**Purpose:** Verifies EMV abonement status  
**Parameters:**
- `--max_count=5000` - Maximum abonements per run
- `--partner_id=1` - Partner identifier
- `--forks=1` - Number of parallel processes
**Output:** `/tkp2/sdbp54devprod/log/check-emv-transaction.log`

### 4. load_emv_stop_list
**Purpose:** Loads EMV stop list data  
**Parameters:**
- `--partner_id=1` - Partner identifier
- `--forks=1` - Number of parallel processes
**Output:** `/tkp2/sdbp54devprod/log/load-emv-stop-list.log`
**Note:** Usually chained with stoplist recalculation

### 5. reload_city
**Purpose:** Reloads city data from NSI (National System of Information)  
**Parameters:**
- `--partner_id=1` - Partner identifier
- `--forks=1` - Number of parallel processes
**Output:** `/tkp2/sdbp54devprod/log/cron-city-load-nsi.log`

### 6. ride_all
**Purpose:** Loads ride/route data with different shift parameters  
**Parameters:**
- `--partner_id=1` - Partner identifier
- `--date_end_shift=0|2` - Date shift parameter (0 for current, 2 for future)
- `--forks=1` - Number of parallel processes
**Output:** `/tkp2/sdbp54devprod/log/cron-ride-all.log`

### 7. waypoints
**Purpose:** Loads route waypoint data  
**Parameters:**
- `--partner_id=1` - Partner identifier
- `--forks=1` - Number of parallel processes
**Output:** `/tkp2/sdbp54devprod/log/cron-waypoint-load-nsi3.log`

## Inputs/Outputs

### Standard Inputs
- `--action` - Required action parameter
- `--partner_id` - Partner identifier (usually 1)
- `--http_host` - API host for requests
- `--forks` - Number of parallel processes
- `--max_count` - Maximum items to process per run
- `--date_end_shift` - Date shift parameter for ride data

### Standard Outputs
- Log files in `/tkp2/sdbp54devprod/log/` directory
- Database updates based on action performed
- API responses and status codes

## Dependencies

### External Systems
- NSI (National System of Information) - For city, ride, and waypoint data
- EMV processing systems - For transaction and abonement data
- Terminal management systems - For terminal data

### Internal Dependencies
- Database connection for data storage
- Logging system for operation tracking
- API infrastructure for external communications

## Special Notes and Warnings

### Critical Warnings
1. **Single Execution:** The `load_all_terminal_data` action has a comment "!!!! вызывать один раз !!!!" (call only once), indicating it should be used carefully
2. **Resource Usage:** High-frequency operations (every 1 minute) may impact system performance
3. **Fork Management:** The `--forks` parameter controls parallel processing and should be tuned based on system capacity

### Performance Considerations
- EMV transaction processing runs every minute with up to 5000 transactions per run
- Multiple actions running simultaneously may compete for resources
- Log file growth should be monitored and managed

### Maintenance Notes
- Some actions in cron-rules are commented out (e.g., standalone `load_emv_stop_list`)
- The script appears to be part of a larger migration to database-driven cron management
- Partner ID is currently hardcoded to 1 but the system supports multiple partners

## Error Handling
- All operations log to separate files for troubleshooting
- Failed operations should be monitored through log analysis
- Database transaction failures may require manual intervention

## Security Considerations
- Script runs with system privileges
- Database credentials must be properly secured
- Log files may contain sensitive operational data

## Related Files
- `admin/cli/cron_recalculate_stoplist.php` - Often chained with `load_emv_stop_list`
- `migrations/seeds/CronTasksSeeder.php` - Contains modernized versions of these tasks
- Various log files for monitoring and debugging
