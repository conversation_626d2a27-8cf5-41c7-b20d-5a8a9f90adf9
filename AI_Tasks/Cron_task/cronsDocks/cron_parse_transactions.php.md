# cron_parse_transactions.php - Transaction Parser

**File Path:** `api-mobile/cli/cron_parse_transactions.php`  
**Type:** Critical transaction processing cron script  
**Status:** Active - High priority system component  

## Purpose and Logic Description

The `cron_parse_transactions.php` script is responsible for parsing large JSON transaction data and converting it into structured database records. This is a critical component of the transaction processing pipeline that handles the transformation of raw transaction data from mobile API endpoints into the system's internal transaction format.

## Schedule/Frequency

**Execution Interval:** Every 1 minute (`*/1 * * * *`)  
**Priority:** High - Critical for real-time transaction processing  
**Downtime Impact:** High - Transaction delays will occur if this script fails  

## Inputs/Outputs

### Required Parameters
- `--http_host` - API host for mobile API (e.g., `api-mobile.sdbp.54dev.tkp2.prod`)

### Input Sources
- Large JSON files containing transaction data from mobile terminals
- Mobile API endpoints providing transaction feeds
- Terminal transaction queues

### Output Destinations
- **Primary Output:** Structured transaction records in database tables
- **Log Output:** `/tkp2/sdbp54devprod/log/cron-parse-terminal-transaction.log`
- **Error Handling:** Failed transactions logged for retry processing

## Processing Logic

### Data Flow
1. **JSON Retrieval:** Fetches large JSON transaction files from mobile API
2. **Parsing:** Converts JSON structure into individual transaction records
3. **Validation:** Validates transaction data integrity and format
4. **Database Storage:** Inserts parsed transactions into appropriate database tables
5. **Status Tracking:** Updates processing status and logs results

### Transaction Types Handled
- EMV card transactions
- Mobile payment transactions
- Terminal-based transactions
- Abonement (subscription) transactions

## Dependencies

### External Systems
- Mobile API infrastructure (`api-mobile` service)
- Terminal management systems
- Payment processing gateways

### Internal Dependencies
- Database connection for transaction storage
- Logging infrastructure
- Transaction validation libraries
- EMV processing modules

### Related Scripts
- `api/cli/api_action.php` with `bill_emv_transaction` action - Processes transactions after parsing
- `api/cli/api_action.php` with `check_emv_abonements` action - Validates abonements
- `admin/cli/cron_recalculate_stoplist.php` - May be triggered by transaction anomalies

## Performance Characteristics

### Processing Volume
- Designed to handle high-volume transaction streams
- Processes transactions in real-time (1-minute intervals)
- Capable of handling large JSON payloads

### Resource Usage
- Memory intensive due to JSON parsing of large files
- Database write-heavy operations
- Network bandwidth for API communication

### Scalability Considerations
- Single-threaded processing (no fork parameter visible)
- May become bottleneck during peak transaction periods
- Consider horizontal scaling if transaction volume increases

## Error Handling and Recovery

### Common Error Scenarios
1. **JSON Format Errors:** Malformed transaction data
2. **Database Connection Issues:** Temporary database unavailability
3. **API Connectivity Problems:** Mobile API service disruptions
4. **Data Validation Failures:** Invalid transaction parameters

### Recovery Mechanisms
- Failed transactions should be queued for retry
- Partial processing capability for large JSON files
- Error logging for manual intervention
- Transaction integrity checks

## Monitoring and Alerting

### Key Metrics to Monitor
- Processing time per execution
- Number of transactions processed per minute
- Error rates and failure patterns
- Log file growth and disk usage
- Database connection health

### Alert Conditions
- Processing time exceeding normal thresholds
- High error rates in transaction parsing
- Database write failures
- API connectivity issues
- Log file errors indicating system problems

## Security Considerations

### Data Protection
- Transaction data contains sensitive financial information
- Proper encryption for data in transit and at rest
- Access controls for log files and database records
- Audit trails for transaction processing

### System Security
- Script runs with elevated privileges
- Database credentials must be secured
- API authentication and authorization
- Network security for mobile API communication

## Maintenance and Operations

### Regular Maintenance Tasks
- Log file rotation and cleanup
- Database performance optimization
- Transaction data archival
- System performance monitoring

### Troubleshooting Guidelines
1. **Check log files** for parsing errors and API issues
2. **Verify database connectivity** and table status
3. **Monitor API endpoint health** and response times
4. **Review transaction data format** for changes
5. **Check system resources** (memory, disk space, CPU)

### Backup and Recovery
- Transaction data backup procedures
- Database recovery plans
- Log file preservation for audit purposes

## Integration Points

### Upstream Systems
- Mobile terminals generating transaction data
- Payment gateways providing transaction feeds
- Mobile application APIs

### Downstream Systems
- Transaction billing systems
- Financial reporting modules
- Fraud detection systems
- Customer account management

## Special Notes and Warnings

### Critical Warnings
1. **High Frequency Execution:** Running every minute requires careful resource management
2. **Data Integrity:** Transaction parsing errors can lead to financial discrepancies
3. **System Dependencies:** Failure affects entire transaction processing pipeline
4. **Performance Impact:** Large JSON files may cause processing delays

### Operational Notes
- Monitor log file growth due to high-frequency execution
- Consider implementing transaction batching for efficiency
- Ensure adequate database connection pooling
- Plan for peak transaction periods (rush hours, special events)

## Future Considerations

### Potential Improvements
- Implement parallel processing for large JSON files
- Add real-time monitoring and alerting
- Consider message queue integration for better scalability
- Implement automated retry mechanisms for failed transactions

### Migration Notes
- Part of the broader cron modernization effort
- May be migrated to database-driven cron management
- Consider containerization for better resource management
